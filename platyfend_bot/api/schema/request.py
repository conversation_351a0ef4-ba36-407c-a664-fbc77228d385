"""
Request schemas for the Platyfend API.
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, HttpUrl


class PullRequestData(BaseModel):
    """
    Pull Request data model for incoming analysis requests.

    This model validates PR data received from Next.js backends,
    ensuring all required fields are present and properly formatted.
    """
    id: int
    number: int
    title: str
    state: str
    draft: bool = False
    locked: bool = False
    mergeable: Optional[bool] = None
    mergeable_state: str = "unknown"
    merged: bool = False
    merged_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime] = None
    body: Optional[str] = None
    html_url: HttpUrl
    diff_url: HttpUrl
    patch_url: HttpUrl