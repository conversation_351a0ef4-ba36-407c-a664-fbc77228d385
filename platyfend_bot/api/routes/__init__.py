"""
Routes initialization module for Platyfend API.

This module centralizes all route registration and provides a clean
interface for adding new route modules to the FastAPI application.
"""

import logging
from fastapi import FastAPI

from . import pr_routes

logger = logging.getLogger(__name__)


def init_routes(app: FastAPI) -> None:
    """
    Initialize and register all API routes.
    
    This function serves as the central point for registering all route modules.
    When adding new route modules, simply import them and add them here.
    
    Args:
        app: FastAPI application instance
    """
    logger.info("Initializing API routes...")
    
    # Pull Request routes
    app.include_router(
        pr_routes.router,
        prefix="/api/v1/pull-requests",
        tags=["Pull Requests"]
    )
    
    # Future route modules can be added here:
    # app.include_router(
    #     analysis_routes.router,
    #     prefix="/api/v1/analysis",
    #     tags=["Analysis"]
    # )
    # 
    # app.include_router(
    #     webhook_routes.router,
    #     prefix="/api/v1/webhooks",
    #     tags=["Webhooks"]
    # )
    
    logger.info("✅ All routes initialized successfully")


# Export commonly used route components
__all__ = [
    "init_routes",
    "pr_routes",
]
