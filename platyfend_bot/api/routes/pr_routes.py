"""
Pull Request Analysis Routes

Professional API endpoints for handling Pull Request security analysis requests.
"""

import logging
from uuid import uuid4

from fastapi import APIRouter, BackgroundTasks, HTTPException, status
from fastapi.responses import JSONResponse

from platyfend_bot.api.schema import AnalysisResponse, PullRequestData
from platyfend_bot.core.analyzers.pull_request import process_pr_analysis

logger = logging.getLogger(__name__)

# Create router instance
router = APIRouter()

@router.post(
    "/analyze",
    response_model=AnalysisResponse,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Analyze Pull Request",
    description="""
    Initiate security analysis for a Pull Request.

    This endpoint receives PR data from Next.js backends and starts a comprehensive
    security analysis including static code analysis, AI-powered vulnerability
    detection, and automated patch generation.

    The analysis runs asynchronously in the background and results are posted
    as comments on the original Pull Request.
    """,
    responses={
        202: {
            "description": "Analysis request accepted and queued for processing",
        },
        400: {
            "description": "Invalid request data or PR state",
        },
        422: {
            "description": "Validation error in request payload",
        },
        500: {
            "description": "Internal server error",
        }
    }
)
async def analyze_pull_request(
    pr_data: PullRequestData,
    background_tasks: BackgroundTasks
) -> AnalysisResponse:
    """
    Analyze a Pull Request for security vulnerabilities.

    Args:
        pr_data: Validated Pull Request data from the request body
        background_tasks: FastAPI background tasks for async processing

    Returns:
        AnalysisResponse: Confirmation that analysis has been queued

    Raises:
        HTTPException: For invalid PR states or processing errors
    """
    request_id = str(uuid4())

    logger.info(f"[{request_id}] Received PR analysis request: PR #{pr_data.number}")

    # Validate PR state
    if pr_data.state == "closed" and not pr_data.merged:
        logger.warning(f"[{request_id}] Rejecting analysis for closed PR #{pr_data.number}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot analyze closed pull requests"
        )

    # Queue background analysis
    background_tasks.add_task(process_pr_analysis, pr_data, request_id)

    logger.info(f"[{request_id}] Analysis queued for PR #{pr_data.number}")

    return AnalysisResponse(
        request_id=request_id,
        message="Pull request analysis initiated successfully",
        pr_id=pr_data.id,
        status="queued"
    )
